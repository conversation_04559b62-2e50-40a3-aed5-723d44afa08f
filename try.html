<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, viewport-fit=cover"
    />
    <title>MeteorVault • Left Rotor with Meteors</title>
    <style>
      /* theme updated to match landing page (dark + warm orange accent) */
      /* Color system (4 total):
         Neutrals: #0b0b0b, #111111
         Accent:   #ff7a1a (warm orange)
         Text:     #e6e6e6
      */
      :root {
        --bg-1: #0b0b0b;
        --bg-2: #111111;
        --text: #e6e6e6;
        --accent: #ff7a1a;      /* warm, subtle accent */
        --ring-color: rgba(230,230,230,0.22);

        --circle-size: 100dvh;  /* full height so semicircle spans top->bottom */
        --dot-size: clamp(30px, 2.4vh, 28px); /* per earlier request */
        --spin-duration: 40s;
      }

      /* Reset and base */
      * { box-sizing: border-box; }
      html, body { height: 100%; }
      body {
        margin: 0;
        color: var(--text);
        background: linear-gradient(180deg, var(--bg-2), var(--bg-1));
        overflow: hidden;
        font-family: system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji";
      }

      /* Layout: left/right halves */
      .layout {
        display: flex;
        height: 100dvh;
        width: 100%;
      }

      .left, .right {
        height: 100%;
      }

      .left {
        flex: 0 0 30%;
        position: relative;
        overflow: hidden; /* only half of the circle visible */
        background: transparent;
      }

      .right {
        flex: 0 0 60%;
        background: transparent;
      }

      /* Rotor: the whole system that spins (ring + meteors) */
      .rotor {
        position: absolute;
        width: var(--circle-size);
        height: var(--circle-size);
        left: 0;
        top: 50%;
        transform: translate(-50%, -50%);
        animation: spin var(--spin-duration) linear infinite;
        will-change: transform;
        backface-visibility: hidden;
        transform-style: preserve-3d;
        filter: none;
      }
      .rotor.paused { animation-play-state: paused; }
      @keyframes spin { to { transform: translate(-50%, -50%) rotate(360deg); } }
      @media (prefers-reduced-motion: reduce) { .rotor { animation: none; } }

      /* Ring */
      .ring {
        position: absolute;
        inset: 0;
        border-radius: 50%;
        border: 2px solid var(--ring-color);
        box-shadow: none;
        background: transparent;
      }
      .ring::after { content: none; } /* remove halo/glow layer entirely */

      .dots { position: absolute; inset: 0; pointer-events: none; }
      .dot-orbit { position: absolute; inset: 0; transform: rotate(var(--angle)); transform-origin: 50% 50%; pointer-events: none; }

      /* Meteors */
      .meteor {
        position: absolute;
        left: 50%;
        top: 0%;
        transform: translate(-50%, -50%) scale(var(--scale, 1));
        width: var(--dot-size);
        height: var(--dot-size);
        border-radius: 50%;
        background: #161616;
        border: 1px solid color-mix(in srgb, var(--ring-color) 75%, transparent);
        cursor: pointer;
        pointer-events: auto;
        transition: transform 180ms ease, border-color 180ms ease, box-shadow 180ms ease, filter 180ms ease;
        outline: none;
      }
      .meteor img {
        width: 100%; height: 100%; border-radius: 50%; object-fit: cover; display: block;
      }
      .meteor:is(:hover, :focus-visible, .is-hover) {
        --scale: 1.5;
        border-color: color-mix(in srgb, var(--accent) 60%, white 10%);
        box-shadow: 0 0 0 3px color-mix(in srgb, var(--accent) 28%, transparent);
        filter: saturate(1.05);
      }
      .meteor:focus-visible { outline: 2px solid var(--accent); outline-offset: 2px; }

      /* Right panel */
      .right-content { display: grid; place-items: center; height: 100%; padding: clamp(16px, 4vw, 64px); }
      .right-card {
        max-width: 620px; width: min(92%, 620px);
        border-radius: 14px;
        background: #121212;
        border: 1px solid rgba(255,255,255,0.06);
        padding: clamp(18px, 2.6vw, 28px);
      }
      .right-header { display: grid; grid-template-columns: 96px 1fr; gap: 16px; align-items: center; margin-bottom: 12px; }
      .right-header img { width: 96px; height: 96px; border-radius: 10px; object-fit: cover; background: #000; }
      .right-card h1 { margin: 0; font-size: 18px; font-weight: 600; }
      .right-card p { margin: 0; color: #c9c9c9; line-height: 1.55; }
    </style>
  </head>
  <body>
    <main class="layout">
      <section class="left" aria-label="Minimal rotating orbit">
        <div class="rotor" aria-hidden="true">
          <div class="ring"></div>
          <div class="dots" id="dots">
            <div class="dot-orbit" style="--i:0;  --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 1"  data-desc="Sample description for Meteor 1"  data-image="/meteor.png?height=96&width=96"><img src="/placeholder.svg?height=64&width=64"  alt="Meteor 1"  /></button></div>
            <div class="dot-orbit" style="--i:1;  --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 2"  data-desc="Sample description for Meteor 2"  data-image="/placeholder.svg?height=96&width=96"><img src="/placeholder.svg?height=64&width=64"  alt="Meteor 2"  /></button></div>
            <div class="dot-orbit" style="--i:2;  --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 3"  data-desc="Sample description for Meteor 3"  data-image="/placeholder.svg?height=96&width=96"><img src="/placeholder.svg?height=64&width=64"  alt="Meteor 3"  /></button></div>
            <div class="dot-orbit" style="--i:3;  --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 4"  data-desc="Sample description for Meteor 4"  data-image="/placeholder.svg?height=96&width=96"><img src="/placeholder.svg?height=64&width=64"  alt="Meteor 4"  /></button></div>
            <div class="dot-orbit" style="--i:4;  --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 5"  data-desc="Sample description for Meteor 5"  data-image="/placeholder.svg?height=96&width=96"><img src="/placeholder.svg?height=64&width=64"  alt="Meteor 5"  /></button></div>
            <div class="dot-orbit" style="--i:5;  --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 6"  data-desc="Sample description for Meteor 6"  data-image="/placeholder.svg?height=96&width=96"><img src="/placeholder.svg?height=64&width=64"  alt="Meteor 6"  /></button></div>
            <div class="dot-orbit" style="--i:6;  --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 7"  data-desc="Sample description for Meteor 7"  data-image="/placeholder.svg?height=96&width=96"><img src="/placeholder.svg?height=64&width=64"  alt="Meteor 7"  /></button></div>
            <div class="dot-orbit" style="--i:7;  --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 8"  data-desc="Sample description for Meteor 8"  data-image="/placeholder.svg?height=96&width=96"><img src="/placeholder.svg?height=64&width=64"  alt="Meteor 8"  /></button></div>
            <div class="dot-orbit" style="--i:8;  --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 9"  data-desc="Sample description for Meteor 9"  data-image="/placeholder.svg?height=96&width=96"><img src="/placeholder.svg?height=64&width=64"  alt="Meteor 9"  /></button></div>
            <div class="dot-orbit" style="--i:9;  --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 10" data-desc="Sample description for Meteor 10" data-image="/placeholder.svg?height=96&width=96"><img src="/placeholder.svg?height=64&width=64" alt="Meteor 10" /></button></div>
            <div class="dot-orbit" style="--i:10; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 11" data-desc="Sample description for Meteor 11" data-image="/placeholder.svg?height=96&width=96"><img src="/placeholder.svg?height=64&width=64" alt="Meteor 11" /></button></div>
            <div class="dot-orbit" style="--i:11; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 12" data-desc="Sample description for Meteor 12" data-image="/placeholder.svg?height=96&width=96"><img src="/placeholder.svg?height=64&width=64" alt="Meteor 12" /></button></div>
            <div class="dot-orbit" style="--i:12; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 13" data-desc="Sample description for Meteor 13" data-image="/placeholder.svg?height=96&width=96"><img src="/placeholder.svg?height=64&width=64" alt="Meteor 13" /></button></div>
            <div class="dot-orbit" style="--i:13; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 14" data-desc="Sample description for Meteor 14" data-image="/placeholder.svg?height=96&width=96"><img src="/placeholder.svg?height=64&width=64" alt="Meteor 14" /></button></div>
            <div class="dot-orbit" style="--i:14; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 15" data-desc="Sample description for Meteor 15" data-image="/placeholder.svg?height=96&width=96"><img src="/placeholder.svg?height=64&width=64" alt="Meteor 15" /></button></div>
            <div class="dot-orbit" style="--i:15; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 16" data-desc="Sample description for Meteor 16" data-image="/placeholder.svg?height=96&width=96"><img src="/placeholder.svg?height=64&width=64" alt="Meteor 16" /></button></div>
            <div class="dot-orbit" style="--i:16; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 17" data-desc="Sample description for Meteor 17" data-image="/placeholder.svg?height=96&width=96"><img src="/placeholder.svg?height=64&width=64" alt="Meteor 17" /></button></div>
            <div class="dot-orbit" style="--i:17; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 18" data-desc="Sample description for Meteor 18" data-image="/placeholder.svg?height=96&width=96"><img src="/placeholder.svg?height=64&width=64" alt="Meteor 18" /></button></div>
            <div class="dot-orbit" style="--i:18; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 19" data-desc="Sample description for Meteor 19" data-image="/placeholder.svg?height=96&width=96"><img src="/placeholder.svg?height=64&width=64" alt="Meteor 19" /></button></div>
            <div class="dot-orbit" style="--i:19; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 20" data-desc="Sample description for Meteor 20" data-image="/placeholder.svg?height=96&width=96"><img src="/placeholder.svg?height=64&width=64" alt="Meteor 20" /></button></div>
            <div class="dot-orbit" style="--i:20; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 21" data-desc="Sample description for Meteor 21" data-image="/placeholder.svg?height=96&width=96"><img src="/placeholder.svg?height=64&width=64" alt="Meteor 21" /></button></div>
            <div class="dot-orbit" style="--i:21; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 22" data-desc="Sample description for Meteor 22" data-image="/placeholder.svg?height=96&width=96"><img src="/placeholder.svg?height=64&width=64" alt="Meteor 22" /></button></div>
            <div class="dot-orbit" style="--i:22; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 23" data-desc="Sample description for Meteor 23" data-image="/placeholder.svg?height=96&width=96"><img src="/placeholder.svg?height=64&width=64" alt="Meteor 23" /></button></div>
            <div class="dot-orbit" style="--i:23; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 24" data-desc="Sample description for Meteor 24" data-image="/placeholder.svg?height=96&width=96"><img src="/placeholder.svg?height=64&width=64" alt="Meteor 24" /></button></div>
            <div class="dot-orbit" style="--i:24; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 25" data-desc="Sample description for Meteor 25" data-image="/placeholder.svg?height=96&width=96"><img src="/placeholder.svg?height=64&width=64" alt="Meteor 25" /></button></div>
            <div class="dot-orbit" style="--i:25; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 26" data-desc="Sample description for Meteor 26" data-image="/placeholder.svg?height=96&width=96"><img src="/placeholder.svg?height=64&width=64" alt="Meteor 26" /></button></div>
            <div class="dot-orbit" style="--i:26; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 27" data-desc="Sample description for Meteor 27" data-image="/placeholder.svg?height=96&width=96"><img src="/placeholder.svg?height=64&width=64" alt="Meteor 27" /></button></div>
            <div class="dot-orbit" style="--i:27; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 28" data-desc="Sample description for Meteor 28" data-image="/placeholder.svg?height=96&width=96"><img src="/placeholder.svg?height=64&width=64" alt="Meteor 28" /></button></div>
            <div class="dot-orbit" style="--i:28; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 29" data-desc="Sample description for Meteor 29" data-image="/placeholder.svg?height=96&width=96"><img src="/placeholder.svg?height=64&width=64" alt="Meteor 29" /></button></div>
            <div class="dot-orbit" style="--i:29; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 30" data-desc="Sample description for Meteor 30" data-image="/placeholder.svg?height=96&width=96"><img src="/placeholder.svg?height=64&width=64" alt="Meteor 30" /></button></div>
          </div>
        </div>
      </section>

      <section class="right" aria-label="Content area">
        <div class="right-content">
          <article class="right-card" role="region" aria-labelledby="detail-title">
            <div class="right-header">
              <img id="info-image" alt="Selected meteor preview" />
              <div>
                <h1 id="info-title"></h1>
                <p id="info-desc"></p>
              </div>
            </div>
            <p style="color:#c9c9c9; margin:0;">
              Click any meteor on the left to update this panel. Hover pauses the orbit and zooms that meteor.
            </p>
          </article>
        </div>
      </section>
    </main>

    <script>
      (function wireMeteors() {
        const rotor = document.querySelector('.rotor');
        const meteors = document.querySelectorAll('.meteor');

        const infoTitle = document.getElementById('hero-title') || document.getElementById('info-title');
        const rightCard = document.querySelector('.right-card');

/*************  ✨ Windsurf Command ⭐  *************/
        /**
         * Updates the right-hand content panel with the given meteor's details.
         * If the panel is not present, the title element is updated instead.
         * @param {Element} el - The meteor element that was interacted with.
         */
/*******  be18ac58-5507-450d-b0b5-a136cfb51622  *******/
        function updateInfo(el) {
          const title = el?.dataset?.title || 'Meteor';
          const desc = el?.dataset?.desc || 'No description available.';
          const img = el?.dataset?.image || el.querySelector('img')?.src;

          if (rightCard) {
            const infoImage = rightCard.querySelector('#info-image');
            const infoH = rightCard.querySelector('#info-title');
            const infoP = rightCard.querySelector('#info-desc');
            if (infoImage && img) { infoImage.src = img; infoImage.alt = title; }
            if (infoH) infoH.textContent = title;
            if (infoP) infoP.textContent = desc;
          }
          if (infoTitle && !rightCard) infoTitle.textContent = title;
        }

        meteors.forEach((m) => {
          m.addEventListener('mouseenter', () => { rotor?.classList.add('paused'); m.classList.add('is-hover'); });
          m.addEventListener('mouseleave', () => { rotor?.classList.remove('paused'); m.classList.remove('is-hover'); });
          m.addEventListener('click', () => updateInfo(m));
          m.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); updateInfo(m); }
          });
          m.setAttribute('tabindex', '0');
          m.setAttribute('aria-label', m.dataset.title || 'Meteor');
        });

        document.addEventListener('visibilitychange', () => {
          if (!rotor) return;
          rotor.style.animationPlayState = document.hidden ? 'paused' : 'running';
        });
      })();
    </script>
  </body>
</html>
