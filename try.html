<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, viewport-fit=cover"
    />
    <title>MeteorVault • Left Rotor with Meteors</title>
    <style>
      /* theme updated to match landing page (dark + warm orange accent) */
      /* Color system (4 total):
         Neutrals: #0b0b0b, #111111
         Accent:   #ff7a1a (warm orange)
         Text:     #e6e6e6
      */
      :root {
        --bg-1: #0b0b0b;
        --bg-2: #111111;
        --text: #e6e6e6;
        --accent: #ff7a1a;      /* warm, subtle accent */
        --ring-color: rgba(230,230,230,0.22);

        --circle-size: 100dvh;  /* full height so semicircle spans top->bottom */
        --dot-size: clamp(30px, 2.4vh, 28px); /* per earlier request */
        --spin-duration: 40s;

        /* Explosion animation variables */
        --space: #0b0d12;
        --white: #ffffff;
        --gray: rgba(255, 255, 255, 0.6);
        --fire: #ff6a00;
        --teal: #00b3b3;
        --dur-explode: 1200ms;
        --dur-video-fade: 600ms;
        --dur-reform: 1400ms;
        --easing: cubic-bezier(0.22, 1, 0.36, 1);
      }

      /* Reset and base */
      * { box-sizing: border-box; }
      html, body { height: 100%; }
      body {
        margin: 0;
        color: var(--text);
        background: linear-gradient(180deg, var(--bg-2), var(--bg-1));
        overflow: hidden;
        font-family: system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji";
      }

      /* Layout: left/right halves */
      .layout {
        display: flex;
        height: 100dvh;
        width: 100%;
      }

      .left, .right {
        height: 100%;
      }

      .left {
        flex: 0 0 30%;
        position: relative;
        overflow: hidden; /* only half of the circle visible */
        background: transparent;
      }

      .right {
        flex: 0 0 60%;
        background: transparent;
      }

      /* Rotor: the whole system that spins (ring + meteors) */
      .rotor {
        position: absolute;
        width: var(--circle-size);
        height: var(--circle-size);
        left: 0;
        top: 50%;
        transform: translate(-50%, -50%);
        animation: spin var(--spin-duration) linear infinite;
        will-change: transform;
        backface-visibility: hidden;
        transform-style: preserve-3d;
        filter: none;
      }
      .rotor.paused { animation-play-state: paused; }
      @keyframes spin { to { transform: translate(-50%, -50%) rotate(360deg); } }
      @media (prefers-reduced-motion: reduce) { .rotor { animation: none; } }

      /* Ring */
      .ring {
        position: absolute;
        inset: 0;
        border-radius: 50%;
        border: 2px solid var(--ring-color);
        box-shadow: none;
        background: transparent;
      }
      .ring::after { content: none; } /* remove halo/glow layer entirely */

      .dots { position: absolute; inset: 0; pointer-events: none; }
      .dot-orbit { position: absolute; inset: 0; transform: rotate(var(--angle)); transform-origin: 50% 50%; pointer-events: none; }

      /* Meteors */
      .meteor {
        position: absolute;
        left: 50%;
        top: 0%;
        transform: translate(-50%, -50%) scale(var(--scale, 1));
        width: var(--dot-size);
        height: var(--dot-size);
        border-radius: 50%;
        background: #161616;
        border: 1px solid color-mix(in srgb, var(--ring-color) 75%, transparent);
        cursor: pointer;
        pointer-events: auto;
        transition: transform 180ms ease, border-color 180ms ease, box-shadow 180ms ease, filter 180ms ease;
        outline: none;
      }
      .meteor img {
        width: 100%; height: 100%; border-radius: 50%; object-fit: cover; display: block;
      }
      .meteor:is(:hover, :focus-visible, .is-hover) {
        --scale: 1.5;
        border-color: color-mix(in srgb, var(--accent) 60%, white 10%);
        box-shadow: 0 0 0 3px color-mix(in srgb, var(--accent) 28%, transparent);
        filter: saturate(1.05);
      }
      .meteor:focus-visible { outline: 2px solid var(--accent); outline-offset: 2px; }

      /* Explosion Scene Overlay */
      .explosion-scene {
        position: fixed;
        inset: 0;
        z-index: 1000;
        pointer-events: none;
        opacity: 0;
        transition: opacity 300ms ease;
        background: rgba(0, 0, 0, 0.1);
      }
      .explosion-scene.active {
        opacity: 1;
        pointer-events: auto;
      }

      /* Letterbox bars */
      .bar {
        position: absolute;
        left: 0; right: 0;
        height: 14%;
        background: linear-gradient(to bottom, rgba(0,0,0,0.9), rgba(0,0,0,0.85));
        z-index: 10;
        transform: translateY(-100%);
        transition: transform 600ms var(--easing);
        will-change: transform;
        pointer-events: none;
      }
      .bar.bottom {
        top: auto; bottom: 0;
        transform: translateY(100%);
        background: linear-gradient(to top, rgba(0,0,0,0.9), rgba(0,0,0,0.85));
      }
      .explosion-scene.exploding .bar.top,
      .explosion-scene.reforming .bar.top {
        transform: translateY(0%);
      }
      .explosion-scene.exploding .bar.bottom,
      .explosion-scene.reforming .bar.bottom {
        transform: translateY(0%);
      }

      /* Shockwave ring */
      .shockwave {
        position: absolute;
        width: 12vmin; height: 12vmin;
        border-radius: 999px;
        transform: translate(-50%, -50%) scale(0.1);
        box-shadow: 0 0 0 2px var(--teal) inset, 0 0 24px rgba(0, 179, 179, 0.9);
        opacity: 0;
        z-index: 5;
        pointer-events: none;
      }
      .explosion-scene.exploding .shockwave {
        animation: boom 900ms var(--easing) forwards;
      }
      @keyframes boom {
        0% { transform: translate(-50%,-50%) scale(0.1); opacity: 0 }
        35% { opacity: 1 }
        100% { transform: translate(-50%,-50%) scale(18); opacity: 0 }
      }

      /* Explosion meteor styles */
      .explosion-meteor {
        position: absolute;
        transform: translate(-50%, -50%);
        z-index: 6;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        pointer-events: none;
      }
      .explosion-meteor img {
        width: min(22vmin, 180px);
        height: auto;
        display: block;
        filter: drop-shadow(0 10px 30px rgba(0,0,0,0.6));
        transform-origin: center;
        animation: idleVibrate 2400ms ease-in-out infinite;
      }
      @keyframes idleVibrate {
        0%, 100% { transform: translate3d(0, 0, 0) rotate(0deg) }
        50% { transform: translate3d(0px, 1px, 0) rotate(-0.4deg) }
      }
      .explosion-scene.exploding .explosion-meteor img {
        animation: intenseVibrate 220ms linear infinite;
      }
      @keyframes intenseVibrate {
        0% { transform: translate3d(-1px, 0px, 0) rotate(-1deg) }
        25% { transform: translate3d(1px, 1px, 0) rotate(1deg) }
        50% { transform: translate3d(0px, -1px, 0) rotate(-0.6deg) }
        75% { transform: translate3d(2px, 0px, 0) rotate(0.8deg) }
        100% { transform: translate3d(-1px, 1px, 0) rotate(-1deg) }
      }

      /* Particles (fire + gas) */
      .particles {
        position: absolute;
        width: 0; height: 0;
        z-index: 3;
        pointer-events: none;
        filter: saturate(1.15);
      }
      .particles.front {
        z-index: 7;
      }
      .particle {
        position: absolute;
        left: 0; top: 0;
        width: 8px; height: 8px;
        border-radius: 999px;
        transform: translate3d(0,0,0) scale(1);
        opacity: 0;
        will-change: transform, opacity, filter;
        backface-visibility: hidden;
      }
      .particle.fire {
        background: radial-gradient(closest-side, #ffd9a3 0%, #ffb23f 45%, #ff6a00 85%, rgba(0,0,0,0) 100%);
        filter: blur(0.2px) drop-shadow(0 0 10px rgba(255, 106, 0, 0.65));
      }
      .particle.gas {
        background: radial-gradient(closest-side, rgba(255, 208, 128, 0.8) 0%, rgba(255, 128, 64, 0.45) 60%, rgba(0,0,0,0) 100%);
        filter: blur(2px);
      }

      /* Explosion outward */
      .particle.explode {
        opacity: 1;
        transition:
          transform var(--dur-explode) var(--easing),
          opacity var(--dur-explode) ease;
        transform: translate3d(var(--tx), var(--ty), 0) scale(var(--sc, 1));
      }

      /* Reform inwards (reverse) */
      .particle.reform {
        transition:
          transform var(--dur-reform) var(--easing),
          opacity var(--dur-reform) ease;
        transform: translate3d(0, 0, 0) scale(0.6);
        opacity: 0.85;
      }

      /* Camera shake on the whole scene during boom */
      .explosion-scene.exploding {
        animation: camShake 550ms ease-in-out;
      }
      @keyframes camShake {
        0% { transform: translate3d(0,0,0) }
        20% { transform: translate3d(2px, -2px, 0) }
        40% { transform: translate3d(-1px, 2px, 0) }
        60% { transform: translate3d(1px, 0px, 0) }
        80% { transform: translate3d(0px, -1px, 0) }
        100% { transform: translate3d(0,0,0) }
      }

      /* Video overlay */
      .video {
        position: absolute;
        inset: 0;
        display: grid;
        place-items: center;
        background: rgba(0,0,0,0.2);
        z-index: 8;
        opacity: 0;
        pointer-events: none;
        transition: opacity var(--dur-video-fade) var(--easing);
      }
      .video video {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      .explosion-scene.playing .video {
        opacity: 1;
        pointer-events: auto;
      }

      /* A11y helper label (visually hidden but readable) */
      .sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0,0,0,0);
        white-space: nowrap;
        border: 0;
      }

      /* Respect prefers-reduced-motion: simplify effects */
      @media (prefers-reduced-motion: reduce) {
        .explosion-meteor img { animation: none }
        .explosion-scene.exploding { animation: none }
      }

      /* Right panel */
      .right-content { display: grid; place-items: center; height: 100%; padding: clamp(16px, 4vw, 64px); }
      .right-card {
        max-width: 620px; width: min(92%, 620px);
        border-radius: 14px;
        background: #121212;
        border: 1px solid rgba(255,255,255,0.06);
        padding: clamp(18px, 2.6vw, 28px);
      }
      .right-header { display: grid; grid-template-columns: 96px 1fr; gap: 16px; align-items: center; margin-bottom: 12px; }
      .right-header img { width: 96px; height: 96px; border-radius: 10px; object-fit: cover; background: #000; }
      .right-card h1 { margin: 0; font-size: 18px; font-weight: 600; }
      .right-card p { margin: 0; color: #c9c9c9; line-height: 1.55; }
    </style>
  </head>
  <body>
    <main class="layout">
      <section class="left" aria-label="Minimal rotating orbit">
        <div class="rotor" aria-hidden="true">
          <div class="ring"></div>
          <div class="dots" id="dots">
            <div class="dot-orbit" style="--i:0;  --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 1"  data-desc="Sample description for Meteor 1"  data-image="meteor.png"><img src="meteor.png"  alt="Meteor 1"  /></button></div>
            <div class="dot-orbit" style="--i:1;  --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 2"  data-desc="Sample description for Meteor 2"  data-image="meteor.png"><img src="meteor.png"  alt="Meteor 2"  /></button></div>
            <div class="dot-orbit" style="--i:2;  --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 3"  data-desc="Sample description for Meteor 3"  data-image="meteor.png"><img src="meteor.png"  alt="Meteor 3"  /></button></div>
            <div class="dot-orbit" style="--i:3;  --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 4"  data-desc="Sample description for Meteor 4"  data-image="meteor.png"><img src="meteor.png"  alt="Meteor 4"  /></button></div>
            <div class="dot-orbit" style="--i:4;  --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 5"  data-desc="Sample description for Meteor 5"  data-image="meteor.png"><img src="meteor.png"  alt="Meteor 5"  /></button></div>
            <div class="dot-orbit" style="--i:5;  --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 6"  data-desc="Sample description for Meteor 6"  data-image="meteor.png"><img src="meteor.png"  alt="Meteor 6"  /></button></div>
            <div class="dot-orbit" style="--i:6;  --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 7"  data-desc="Sample description for Meteor 7"  data-image="meteor.png"><img src="meteor.png"  alt="Meteor 7"  /></button></div>
            <div class="dot-orbit" style="--i:7;  --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 8"  data-desc="Sample description for Meteor 8"  data-image="meteor.png"><img src="meteor.png"  alt="Meteor 8"  /></button></div>
            <div class="dot-orbit" style="--i:8;  --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 9"  data-desc="Sample description for Meteor 9"  data-image="meteor.png"><img src="meteor.png"  alt="Meteor 9"  /></button></div>
            <div class="dot-orbit" style="--i:9;  --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 10" data-desc="Sample description for Meteor 10" data-image="meteor.png"><img src="meteor.png" alt="Meteor 10" /></button></div>
            <div class="dot-orbit" style="--i:10; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 11" data-desc="Sample description for Meteor 11" data-image="meteor.png"><img src="meteor.png" alt="Meteor 11" /></button></div>
            <div class="dot-orbit" style="--i:11; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 12" data-desc="Sample description for Meteor 12" data-image="meteor.png"><img src="meteor.png" alt="Meteor 12" /></button></div>
            <div class="dot-orbit" style="--i:12; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 13" data-desc="Sample description for Meteor 13" data-image="meteor.png"><img src="meteor.png" alt="Meteor 13" /></button></div>
            <div class="dot-orbit" style="--i:13; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 14" data-desc="Sample description for Meteor 14" data-image="meteor.png"><img src="meteor.png" alt="Meteor 14" /></button></div>
            <div class="dot-orbit" style="--i:14; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 15" data-desc="Sample description for Meteor 15" data-image="meteor.png"><img src="meteor.png" alt="Meteor 15" /></button></div>
            <div class="dot-orbit" style="--i:15; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 16" data-desc="Sample description for Meteor 16" data-image="meteor.png"><img src="meteor.png" alt="Meteor 16" /></button></div>
            <div class="dot-orbit" style="--i:16; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 17" data-desc="Sample description for Meteor 17" data-image="meteor.png"><img src="meteor.png" alt="Meteor 17" /></button></div>
            <div class="dot-orbit" style="--i:17; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 18" data-desc="Sample description for Meteor 18" data-image="meteor.png"><img src="meteor.png" alt="Meteor 18" /></button></div>
            <div class="dot-orbit" style="--i:18; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 19" data-desc="Sample description for Meteor 19" data-image="meteor.png"><img src="meteor.png" alt="Meteor 19" /></button></div>
            <div class="dot-orbit" style="--i:19; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 20" data-desc="Sample description for Meteor 20" data-image="meteor.png"><img src="meteor.png" alt="Meteor 20" /></button></div>
            <div class="dot-orbit" style="--i:20; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 21" data-desc="Sample description for Meteor 21" data-image="meteor.png"><img src="meteor.png" alt="Meteor 21" /></button></div>
            <div class="dot-orbit" style="--i:21; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 22" data-desc="Sample description for Meteor 22" data-image="meteor.png"><img src="meteor.png" alt="Meteor 22" /></button></div>
            <div class="dot-orbit" style="--i:22; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 23" data-desc="Sample description for Meteor 23" data-image="meteor.png"><img src="meteor.png" alt="Meteor 23" /></button></div>
            <div class="dot-orbit" style="--i:23; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 24" data-desc="Sample description for Meteor 24" data-image="meteor.png"><img src="meteor.png" alt="Meteor 24" /></button></div>
            <div class="dot-orbit" style="--i:24; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 25" data-desc="Sample description for Meteor 25" data-image="meteor.png"><img src="meteor.png" alt="Meteor 25" /></button></div>
            <div class="dot-orbit" style="--i:25; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 26" data-desc="Sample description for Meteor 26" data-image="meteor.png"><img src="meteor.png" alt="Meteor 26" /></button></div>
            <div class="dot-orbit" style="--i:26; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 27" data-desc="Sample description for Meteor 27" data-image="meteor.png"><img src="meteor.png" alt="Meteor 27" /></button></div>
            <div class="dot-orbit" style="--i:27; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 28" data-desc="Sample description for Meteor 28" data-image="meteor.png"><img src="meteor.png" alt="Meteor 28" /></button></div>
            <div class="dot-orbit" style="--i:28; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 29" data-desc="Sample description for Meteor 29" data-image="meteor.png"><img src="meteor.png" alt="Meteor 29" /></button></div>
            <div class="dot-orbit" style="--i:29; --angle: calc(var(--i) * 12deg);"><button class="meteor" data-title="Meteor 30" data-desc="Sample description for Meteor 30" data-image="meteor.png"><img src="meteor.png" alt="Meteor 30" /></button></div>
          </div>
        </div>
      </section>

      <section class="right" aria-label="Content area">
        <div class="right-content">
          <article class="right-card" role="region" aria-labelledby="detail-title">
            <div class="right-header">
              <img id="info-image" alt="Selected meteor preview" />
              <div>
                <h1 id="info-title"></h1>
                <p id="info-desc"></p>
              </div>
            </div>
            <p style="color:#c9c9c9; margin:0;">
              Click any meteor on the left to update this panel. Hover pauses the orbit and zooms that meteor.
            </p>
          </article>
        </div>
      </section>
    </main>

    <!-- Explosion Scene Overlay -->
    <div id="explosionScene" class="explosion-scene" aria-live="polite">
      <!-- Letterbox bars -->
      <div class="bar top" aria-hidden="true"></div>
      <div class="bar bottom" aria-hidden="true"></div>

      <!-- Shockwave -->
      <div id="shockwave" class="shockwave" aria-hidden="true"></div>

      <!-- Particles container -->
      <div id="explosionParticles" class="particles" aria-hidden="true"></div>

      <!-- Explosion meteor (will be positioned dynamically) -->
      <div id="explosionMeteor" class="explosion-meteor">
        <img src="meteor.png" alt="Exploding Meteor" />
        <span class="sr-only">Meteor explosion in progress</span>
      </div>

      <!-- Video overlay -->
      <div class="video" id="videoOverlay" aria-hidden="true">
        <video id="explosionClip" preload="auto" playsinline>
          <source src="background1.mp4" type="video/mp4" />
          <!-- Fallback content if video doesn't load -->
        </video>
        <div style="color: white; text-align: center; padding: 20px; font-size: 18px;">
          🌟 EXPLOSION! 🌟<br>
          <small>(Video file not found - showing text animation)</small>
        </div>
      </div>
    </div>

    <script>
      // Explosion System
      (function explosionSystem() {
        const explosionScene = document.getElementById('explosionScene');
        const explosionMeteor = document.getElementById('explosionMeteor');
        const explosionParticles = document.getElementById('explosionParticles');
        const shockwave = document.getElementById('shockwave');
        const videoOverlay = document.getElementById('videoOverlay');
        const explosionClip = document.getElementById('explosionClip');
        const prefersReduced = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

        const STATE = { idle: 'idle', exploding: 'exploding', playing: 'playing', reforming: 'reforming' };
        let explosionState = STATE.idle;
        let particlesMade = false;

        function setExplosionState(next) {
          explosionState = next;
        }

        function rand(min, max) {
          return Math.random() * (max - min) + min;
        }

        function createParticles() {
          if (particlesMade) return;
          const count = 120;
          const frag = document.createDocumentFragment();

          for (let i = 0; i < count; i++) {
            const p = document.createElement('div');
            p.className = 'particle ' + (Math.random() < 0.65 ? 'fire' : 'gas');

            const angle = rand(0, Math.PI * 2);
            const radius = rand(80, 360);
            const tx = Math.cos(angle) * radius;
            const ty = Math.sin(angle) * radius;
            const sc = rand(0.6, 1.8);

            p.style.setProperty('--tx', tx.toFixed(1) + 'px');
            p.style.setProperty('--ty', ty.toFixed(1) + 'px');
            p.style.setProperty('--sc', sc.toFixed(2));

            p.style.left = rand(-6, 6) + 'px';
            p.style.top = rand(-6, 6) + 'px';
            p.style.transitionDelay = Math.floor(rand(0, 120)) + 'ms';

            frag.appendChild(p);
          }

          explosionParticles.appendChild(frag);
          particlesMade = true;
        }

        function explodeMeteor(meteorElement) {
          if (explosionState !== STATE.idle) return;
          console.log('Starting explosion animation');
          setExplosionState(STATE.exploding);

          // Get meteor position relative to viewport
          const meteorRect = meteorElement.getBoundingClientRect();
          const centerX = meteorRect.left + meteorRect.width / 2;
          const centerY = meteorRect.top + meteorRect.height / 2;

          // Pause the rotor during explosion to freeze meteor positions
          const rotor = document.querySelector('.rotor');
          if (rotor) {
            rotor.classList.add('paused');
          }

          // Position explosion elements at meteor location
          explosionMeteor.style.left = centerX + 'px';
          explosionMeteor.style.top = centerY + 'px';
          explosionParticles.style.left = centerX + 'px';
          explosionParticles.style.top = centerY + 'px';
          shockwave.style.left = centerX + 'px';
          shockwave.style.top = centerY + 'px';

          // Hide the original meteor during explosion
          meteorElement.style.opacity = '0';
          meteorElement.style.transition = 'opacity 200ms ease';

          // Show explosion scene
          explosionScene.classList.add('active');
          explosionScene.classList.add('exploding');

          // Create and trigger particles
          createParticles();
          explosionParticles.classList.add('front');

          requestAnimationFrame(() => {
            for (const p of explosionParticles.children) {
              p.classList.remove('reform');
              p.classList.add('explode');
            }
          });

          if (prefersReduced) {
            playVideoSoon(150);
          } else {
            playVideoSoon(1000);
          }
        }

        function playVideoSoon(delay) {
          setTimeout(() => {
            playVideo();
          }, delay);
        }

        function playVideo() {
          setExplosionState(STATE.playing);
          explosionScene.classList.add('playing');
          videoOverlay.setAttribute('aria-hidden', 'false');

          const playPromise = explosionClip.play();
          if (playPromise && typeof playPromise.catch === 'function') {
            playPromise.catch(() => {
              // Video failed to play, show fallback and auto-advance after delay
              console.log('Video playback failed, using fallback');
              setTimeout(() => {
                startReform();
              }, 3000); // 3 second fallback duration
            });
          }

          // Fallback: if video doesn't start playing within 1 second, assume no video
          setTimeout(() => {
            if (explosionClip.paused && explosionClip.currentTime === 0) {
              console.log('Video not playing, using fallback timing');
              setTimeout(() => {
                startReform();
              }, 2000); // 2 more seconds for fallback
            }
          }, 1000);
        }

        function startReform() {
          if (explosionState !== STATE.playing) return;
          setExplosionState(STATE.reforming);

          explosionScene.classList.remove('exploding');
          explosionScene.classList.add('reforming');
          explosionParticles.classList.remove('front');

          const particles = explosionParticles.children;
          for (const p of particles) {
            p.classList.remove('explode');
            p.style.transitionDelay = Math.floor(rand(0, 120)) + 'ms';
            p.classList.add('reform');
          }

          explosionScene.classList.remove('playing');
          explosionClip.pause();
          explosionClip.currentTime = 0;

          setTimeout(() => {
            finishReset();
          }, parseInt(getComputedStyle(document.documentElement).getPropertyValue('--dur-reform')) || 1400);
        }

        function finishReset() {
          explosionParticles.classList.remove('front');
          videoOverlay.setAttribute('aria-hidden', 'true');
          explosionScene.classList.remove('reforming');
          explosionScene.classList.remove('active');

          for (const p of explosionParticles.children) {
            p.classList.remove('explode');
            p.classList.add('reform');
          }

          // Restore rotor animation and meteor visibility
          const rotor = document.querySelector('.rotor');
          if (rotor) {
            rotor.classList.remove('paused');
          }

          // Restore all meteor visibility
          const meteors = document.querySelectorAll('.meteor');
          meteors.forEach(m => {
            m.style.opacity = '';
            m.style.transition = '';
          });

          setExplosionState(STATE.idle);
        }

        // Video event listeners
        explosionClip.addEventListener('ended', () => {
          startReform();
        });

        explosionClip.addEventListener('play', () => {
          setTimeout(() => {
            if (explosionState === STATE.playing && !explosionClip.paused) {
              explosionClip.pause();
              startReform();
            }
          }, 11000);
        });

        // Handle video load errors gracefully
        explosionClip.addEventListener('error', () => {
          console.log('Video failed to load, using text fallback');
        });

        // Expose explosion function globally
        window.triggerExplosion = explodeMeteor;
        window.explosionReset = finishReset;
      })();

      (function wireMeteors() {
        const rotor = document.querySelector('.rotor');
        const meteors = document.querySelectorAll('.meteor');

        const infoTitle = document.getElementById('hero-title') || document.getElementById('info-title');
        const rightCard = document.querySelector('.right-card');

/*************  ✨ Windsurf Command ⭐  *************/
        /**
         * Updates the right-hand content panel with the given meteor's details.
         * If the panel is not present, the title element is updated instead.
         * @param {Element} el - The meteor element that was interacted with.
         */
/*******  be18ac58-5507-450d-b0b5-a136cfb51622  *******/
        function updateInfo(el) {
          const title = el?.dataset?.title || 'Meteor';
          const desc = el?.dataset?.desc || 'No description available.';
          const img = el?.dataset?.image || el.querySelector('img')?.src;

          if (rightCard) {
            const infoImage = rightCard.querySelector('#info-image');
            const infoH = rightCard.querySelector('#info-title');
            const infoP = rightCard.querySelector('#info-desc');
            if (infoImage && img) { infoImage.src = img; infoImage.alt = title; }
            if (infoH) infoH.textContent = title;
            if (infoP) infoP.textContent = desc;
          }
          if (infoTitle && !rightCard) infoTitle.textContent = title;
        }

        meteors.forEach((m) => {
          m.addEventListener('mouseenter', () => { rotor?.classList.add('paused'); m.classList.add('is-hover'); });
          m.addEventListener('mouseleave', () => { rotor?.classList.remove('paused'); m.classList.remove('is-hover'); });
          m.addEventListener('click', () => {
            updateInfo(m);
            // Trigger explosion animation
            if (window.triggerExplosion) {
              window.triggerExplosion(m);
            }
          });
          m.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              updateInfo(m);
              // Trigger explosion animation
              if (window.triggerExplosion) {
                window.triggerExplosion(m);
              }
            }
          });
          m.setAttribute('tabindex', '0');
          m.setAttribute('aria-label', m.dataset.title || 'Meteor');
        });

        document.addEventListener('visibilitychange', () => {
          if (!rotor) return;
          rotor.style.animationPlayState = document.hidden ? 'paused' : 'running';
        });
      })();
    </script>
  </body>
</html>
