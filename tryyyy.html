<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>Meteor Cinematic Boom</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style>
      /* Color system (max 5):
         1) Space Black: #0b0d12
         2) Neutral White: #ffffff
         3) Neutral Gray: rgba(255,255,255,0.6)
         4) Fire Orange: #ff6a00
         5) Teal Shockwave: #00b3b3
      */

      :root {
        --space: #0b0d12;
        --white: #ffffff;
        --gray: rgba(255, 255, 255, 0.6);
        --fire: #ff6a00;
        --teal: #00b3b3;

        --dur-explode: 1200ms;
        --dur-video-fade: 600ms;
        --dur-reform: 1400ms;
        --easing: cubic-bezier(0.22, 1, 0.36, 1);
      }

      html, body {
        height: 100%;
        background: var(--space);
        margin: 0;
        color: var(--white);
        font-family: system-ui, -apple-system, Segoe UI, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, "Apple Color Emoji", "Segoe UI Emoji";
      }

      .wrap {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 100%;
        padding: 16px;
      }

      .scene {
        position: relative;
        width: min(100%, 960px);
        aspect-ratio: 16/9;
        border-radius: 16px;
        overflow: hidden;
        background: radial-gradient(120% 120% at 50% 50%, #111721 0%, #0b0d12 60%, #05070a 100%);
        box-shadow: 0 10px 40px rgba(0,0,0,0.6);
        outline: 1px solid rgba(255,255,255,0.06);
        transform: translateZ(0);
      }

      /* Starfield layers */
      .scene::before,
      .scene::after {
        content: "";
        position: absolute;
        inset: 0;
        background-repeat: repeat;
        pointer-events: none;
      }
      .scene::before {
        background-image:
          radial-gradient(1px 1px at 10% 20%, rgba(255,255,255,0.8) 99%, transparent 100%),
          radial-gradient(1px 1px at 80% 30%, rgba(255,255,255,0.6) 99%, transparent 100%),
          radial-gradient(1px 1px at 50% 80%, rgba(255,255,255,0.65) 99%, transparent 100%),
          radial-gradient(1px 1px at 30% 60%, rgba(255,255,255,0.5) 99%, transparent 100%),
          radial-gradient(1px 1px at 70% 70%, rgba(255,255,255,0.7) 99%, transparent 100%);
        opacity: 0.7;
        z-index: 1;
        animation: starsDrift 50s linear infinite;
      }
      .scene::after {
        background-image:
          radial-gradient(1px 1px at 15% 50%, rgba(255,255,255,0.4) 99%, transparent 100%),
          radial-gradient(1px 1px at 65% 15%, rgba(255,255,255,0.35) 99%, transparent 100%),
          radial-gradient(1px 1px at 85% 80%, rgba(255,255,255,0.3) 99%, transparent 100%);
        opacity: 0.5;
        z-index: 2;
        animation: starsDrift 80s linear infinite reverse;
      }
      @keyframes starsDrift {
        0% { transform: translate3d(0,0,0) }
        100% { transform: translate3d(-200px,0,0) }
      }

      /* Letterbox bars */
      .bar {
        position: absolute;
        left: 0; right: 0;
        height: 14%;
        background: linear-gradient(to bottom, rgba(0,0,0,0.9), rgba(0,0,0,0.85));
        z-index: 10;
        transform: translateY(-100%);
        transition: transform 600ms var(--easing);
        will-change: transform;
        pointer-events: none;
      }
      .bar.bottom {
        top: auto; bottom: 0;
        transform: translateY(100%);
        background: linear-gradient(to top, rgba(0,0,0,0.9), rgba(0,0,0,0.85));
      }
      .scene.exploding .bar.top,
      .scene.reforming .bar.top {
        transform: translateY(0%);
      }
      .scene.exploding .bar.bottom,
      .scene.reforming .bar.bottom {
        transform: translateY(0%);
      }

      /* Shockwave ring */
      .shockwave {
        position: absolute;
        left: 50%; top: 50%;
        width: 12vmin; height: 12vmin;
        border-radius: 999px;
        transform: translate(-50%, -50%) scale(0.1);
        box-shadow: 0 0 0 2px var(--teal) inset, 0 0 24px rgba(0, 179, 179, 0.9);
        opacity: 0;
        z-index: 5;
        pointer-events: none;
      }
      .scene.exploding .shockwave {
        animation: boom 900ms var(--easing) forwards;
      }
      @keyframes boom {
        0% { transform: translate(-50%,-50%) scale(0.1); opacity: 0 }
        35% { opacity: 1 }
        100% { transform: translate(-50%,-50%) scale(18); opacity: 0 }
      }

      /* Meteor */
      .meteor {
        position: absolute;
        left: 50%; top: 50%;
        transform: translate(-50%, -50%);
        z-index: 6; /* Keep above particles by default */
        display: inline-flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        border: none;
        background: none;
        padding: 0;
      }
      .meteor img {
        width: min(22vmin, 180px);
        height: auto;
        display: block;
        filter: drop-shadow(0 10px 30px rgba(0,0,0,0.6));
        transform-origin: center;
        animation: idleVibrate 2400ms ease-in-out infinite;
      }
      @keyframes idleVibrate {
        0%, 100% { transform: translate3d(0, 0, 0) rotate(0deg) }
        50% { transform: translate3d(0px, 1px, 0) rotate(-0.4deg) }
      }
      .scene.exploding .meteor img {
        animation: intenseVibrate 220ms linear infinite;
      }
      @keyframes intenseVibrate {
        0% { transform: translate3d(-1px, 0px, 0) rotate(-1deg) }
        25% { transform: translate3d(1px, 1px, 0) rotate(1deg) }
        50% { transform: translate3d(0px, -1px, 0) rotate(-0.6deg) }
        75% { transform: translate3d(2px, 0px, 0) rotate(0.8deg) }
        100% { transform: translate3d(-1px, 1px, 0) rotate(-1deg) }
      }

      /* Particles (fire + gas) */
      .particles {
        position: absolute;
        left: 50%; top: 50%;
        width: 0; height: 0;
        z-index: 3; /* Behind meteor by default */
        pointer-events: none;
        filter: saturate(1.15);
      }
      .particles.front {
        z-index: 7; /* During explosion, bring some fire above if desired */
      }
      .particle {
        position: absolute;
        left: 0; top: 0;
        width: 8px; height: 8px;
        border-radius: 999px;
        transform: translate3d(0,0,0) scale(1);
        opacity: 0;
        will-change: transform, opacity, filter;
        backface-visibility: hidden;
      }
      .particle.fire {
        background: radial-gradient(closest-side, #ffd9a3 0%, #ffb23f 45%, #ff6a00 85%, rgba(0,0,0,0) 100%);
        filter: blur(0.2px) drop-shadow(0 0 10px rgba(255, 106, 0, 0.65));
      }
      .particle.gas {
        background: radial-gradient(closest-side, rgba(255, 208, 128, 0.8) 0%, rgba(255, 128, 64, 0.45) 60%, rgba(0,0,0,0) 100%);
        filter: blur(2px);
      }

      /* Explosion outward */
      .particle.explode {
        opacity: 1;
        transition:
          transform var(--dur-explode) var(--easing),
          opacity var(--dur-explode) ease;
        transform: translate3d(var(--tx), var(--ty), 0) scale(var(--sc, 1));
      }

      /* Reform inwards (reverse) */
      .particle.reform {
        transition:
          transform var(--dur-reform) var(--easing),
          opacity var(--dur-reform) ease;
        transform: translate3d(0, 0, 0) scale(0.6);
        opacity: 0.85;
      }

      /* Camera shake on the whole scene during boom */
      .scene.exploding {
        animation: camShake 550ms ease-in-out;
      }
      @keyframes camShake {
        0% { transform: translate3d(0,0,0) }
        20% { transform: translate3d(2px, -2px, 0) }
        40% { transform: translate3d(-1px, 2px, 0) }
        60% { transform: translate3d(1px, 0px, 0) }
        80% { transform: translate3d(0px, -1px, 0) }
        100% { transform: translate3d(0,0,0) }
      }

      /* Video overlay */
      .video {
        position: absolute;
        inset: 0;
        display: grid;
        place-items: center;
        background: rgba(0,0,0,0.2);
        z-index: 8;
        opacity: 0;
        pointer-events: none;
        transition: opacity var(--dur-video-fade) var(--easing);
      }
      .video video {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      .scene.playing .video {
        opacity: 1;
        pointer-events: auto;
      }

      /* A11y helper label (visually hidden but readable) */
      .sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0,0,0,0);
        white-space: nowrap;
        border: 0;
      }

      /* Respect prefers-reduced-motion: simplify effects */
      @media (prefers-reduced-motion: reduce) {
        .scene::before, .scene::after { animation: none }
        .meteor img { animation: none }
        .scene.exploding { animation: none }
      }
    </style>
  </head>
  <body>
    <div class="wrap">
      <div id="scene" class="scene" aria-live="polite">
         <!-- Letterbox bars  -->
        <div class="bar top" aria-hidden="true"></div>
        <div class="bar bottom" aria-hidden="true"></div>

         <!-- Shockwave  -->
        <div class="shockwave" aria-hidden="true"></div>

         <!-- Particles (default behind the meteor). We may temporarily add .front during boom.  -->
        <div id="particles" class="particles" aria-hidden="true"></div>

        <button id="meteor" class="meteor" aria-label="Trigger meteor boom" title="Trigger meteor boom">
          <img id="meteor-img" src="meteor.png" alt="Meteor" />
          <span class="sr-only">Activate cinematic meteor explosion</span>
        </button>
<!-- 
         Video overlay (replace clip.mp4 with ~10s video)  -->
        <div class="video" id="videoOverlay" aria-hidden="true">
          <video id="clip" preload="auto" playsinline>
            <source src="background1.mp4" type="video/mp4" />
          </video>
        </div>
      </div>
    </div>

    <script>
      (function () {
        const scene = document.getElementById('scene');
        const meteorBtn = document.getElementById('meteor');
        const particlesEl = document.getElementById('particles');
        const videoOverlay = document.getElementById('videoOverlay');
        const clip = document.getElementById('clip');
        const prefersReduced = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

        const STATE = { idle: 'idle', exploding: 'exploding', playing: 'playing', reforming: 'reforming' };
        let state = STATE.idle;
        let particlesMade = false;

        function setState(next) {
          state = next;
          // console.log("[v0] state:", state);
        }

        function rand(min, max) {
          return Math.random() * (max - min) + min;
        }

        function createParticles() {
          if (particlesMade) return;
          const count = 120; // mix fire + gas
          const frag = document.createDocumentFragment();

          for (let i = 0; i < count; i++) {
            const p = document.createElement('div');
            p.className = 'particle ' + (Math.random() < 0.65 ? 'fire' : 'gas');

            // Random angle / radius for the outward explosion
            const angle = rand(0, Math.PI * 2);
            const radius = rand(80, 360); // px
            const tx = Math.cos(angle) * radius;
            const ty = Math.sin(angle) * radius;
            const sc = rand(0.6, 1.8);

            p.style.setProperty('--tx', tx.toFixed(1) + 'px');
            p.style.setProperty('--ty', ty.toFixed(1) + 'px');
            p.style.setProperty('--sc', sc.toFixed(2));

            // Slight random start offsets
            p.style.left = rand(-6, 6) + 'px';
            p.style.top = rand(-6, 6) + 'px';

            // Stagger outward for a more organic feel
            p.style.transitionDelay = Math.floor(rand(0, 120)) + 'ms';

            frag.appendChild(p);
          }

          particlesEl.appendChild(frag);
          particlesMade = true;
        }

        function explode() {
          if (state !== STATE.idle) return;
          setState(STATE.exploding);

          // Letterbox + camera shake via class
          scene.classList.add('exploding');

          // Ensure particles exist and bring them to front during explosion
          createParticles();
          particlesEl.classList.add('front');

          // Trigger outward movement
          requestAnimationFrame(() => {
            for (const p of particlesEl.children) {
              p.classList.remove('reform');
              p.classList.add('explode');
            }
          });

          // Show shockwave via CSS animation (handled by .scene.exploding .shockwave)

          if (prefersReduced) {
            // Skip intense motion: just fade up video
            playVideoSoon(150);
          } else {
            // Let the explosion breathe for ~1s, then start video fade
            playVideoSoon(1000);
          }
        }

        function playVideoSoon(delay) {
          setTimeout(() => {
            playVideo();
          }, delay);
        }

        function playVideo() {
          setState(STATE.playing);
          scene.classList.add('playing');
          videoOverlay.setAttribute('aria-hidden', 'false');

          // Try to play; fall back if blocked by browser policies
          const playPromise = clip.play();
          if (playPromise && typeof playPromise.catch === 'function') {
            playPromise.catch(() => {
              // Autoplay blocked – show controls so the user can start
              clip.setAttribute('controls', 'controls');
            });
          }
        }

        function startReform() {
          if (state !== STATE.playing) return;
          setState(STATE.reforming);

          // Bars remain during reform for cinematic continuity
          scene.classList.remove('exploding');
          scene.classList.add('reforming');

          // Critical: put particles behind meteor during re-collection
          particlesEl.classList.remove('front');

          // Reverse particle transforms back to center
          const particles = particlesEl.children;
          for (const p of particles) {
            // Remove explode, then apply reform to animate back inward smoothly
            p.classList.remove('explode');
            // slight stagger on return
            p.style.transitionDelay = Math.floor(rand(0, 120)) + 'ms';
            p.classList.add('reform');
          }

          // Fade out the video overlay
          scene.classList.remove('playing');
          clip.pause();
          clip.currentTime = 0;

          // After reform completes, fully reset to idle with meteor on top
          setTimeout(() => {
            finishReset();
          }, parseInt(getComputedStyle(document.documentElement).getPropertyValue('--dur-reform')) || 1400);
        }

        function finishReset() {
          // Ensure meteor is above particles on reset (user's requirement)
          // Achieved by default z-index: meteor(6) > particles(3)
          // Explicitly ensure the stacking order now:
          particlesEl.classList.remove('front');
          // Hide video overlay semantics
          videoOverlay.setAttribute('aria-hidden', 'true');

          // Clear reform state; retract bars
          scene.classList.remove('reforming');

          // Leave particles in DOM (so next explosion is instant), but keep them centered and subtle
          for (const p of particlesEl.children) {
            p.classList.remove('explode');
            p.classList.add('reform');
          }

          setState(STATE.idle);
        }

        // Event wiring
        meteorBtn.addEventListener('click', () => {
          if (state === STATE.idle) explode();
        });
        meteorBtn.addEventListener('keydown', (e) => {
          if ((e.key === 'Enter' || e.key === ' ') && state === STATE.idle) {
            e.preventDefault();
            explode();
          }
        });

        // When video ends (~10s), begin the re-collection
        clip.addEventListener('ended', () => {
          startReform();
        });

        // Safety: if the video source is longer, still revert after 11s
        clip.addEventListener('play', () => {
          setTimeout(() => {
            if (state === STATE.playing && !clip.paused) {
              clip.pause();
              startReform();
            }
          }, 11000);
        });

        // If user clicks during playing/reforming, do nothing (prevents re-entry conflicts)

        // Optional: expose a reset if needed (not required)
        window.meteorReset = finishReset;
      })();
    </script>
  </body>
</html>
